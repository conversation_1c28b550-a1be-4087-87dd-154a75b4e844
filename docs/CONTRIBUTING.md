# Contributing to FlyFit

This guide covers our development workflow, coding standards, and contribution process. **Follow these guidelines to ensure smooth collaboration and maintain code quality.**

> [!IMPORTANT]
> **Prerequisites Required**: Complete the [Getting Started Guide](./GETTING_STARTED.md) before contributing. This guide assumes you have a fully configured development environment.

## Overview

Our development process emphasizes:
- **Trunk-based development** with short-lived feature branches
- **Incremental delivery** over prolonged feature development
- **Quality gates** with automated testing and code review
- **Clear communication** through detailed task planning

## Quick Reference

**Common Commands:**
```bash
npm run lint           # Fix linting issues
npm run typecheck      # Fix typecheck errors
npm run test           # Run unit tests
npm run doctor         # Validate native dependencies for Expo
npm run test:ci        # Run all of the above quality checks at once
npm start              # Start development server
```

**Key Resources:**
- [Trello Board](https://trello.com/b/Z6sPJTtg/fly-fit-kanban-board) - Task management and story assignment
- [Architecture Guide](./ARCHITECTURE.md) - Technical patterns and system design
- [Deployment Guide](./DEPLOYMENT.md) - How to deploy your changes
- [Operations Guide](./OPERATIONS.md) - Project links and monitoring dashboards

## Feature Development Workflow

> [!NOTE]
> **Planning before coding**: We prioritize thorough planning and understanding before implementation to reduce rework and ensure quality.

### Phase 1: Planning & Understanding

1. **Get assigned a story** from the [Trello Kanban board](https://trello.com/b/Z6sPJTtg/fly-fit-kanban-board)

2. **Read and understand the story completely**
    - Review all requirements and acceptance criteria
    - Understand the business context and user impact
    - Identify any dependencies or blockers

3. **Create detailed task breakdown**
    - Use Trello's "checklist" feature on the card
    - Include hour estimates for each task
    - Break down into small, manageable chunks
    - There should never be a task longer than 4 hours, and if so, break it down into smaller tasks

4. **Validate your plan**
    - > Weeks of coding can save hours of planning.
    - ^ understand this joke above
    - Review tasks and estimates with another developer
    - Ask for clarity on requirements, scope, and timeline if unclear
    - Ensure you understand the definition of "done"

5. **Complete non-code tasks first**
    - Research, design, or discovery tasks
    - API design or database schema planning
    - UI/UX mockups or wireframes

> [!WARNING]
> **No coding until planning is complete**: Only begin development after completing all non-code tasks and having clear requirements.

### Phase 2: Development

6. **Create a feature branch**
    ```bash
    git checkout main
    git pull origin main
    git checkout -b feature-name
    ```

7. **Implement incrementally**
    - Make small, focused commits as you progress
    - Commit working code frequently (see [Commit Messages](#commit-messages))
    - Push to remote branch regularly

8. **Test your implementation**
    - Manual testing on both iOS and Android
    - Unit tests for complex business logic
    - Edge case validation

### Phase 3: Quality & Review

9. **Run quality checks locally**
    ```bash
    npm run test:ci  # Must pass before creating PR
    ```

10. **Create pull request**
    - Fill out the PR template completely
    - Include screenshots/videos for UI changes
    - Reference the Trello card

11. **Code review process**
    - Request review from appropriate team members
    - Respond to feedback promptly
    - Make requested changes and re-request review

12. **Merge and deploy**
    - Merge as soon as you have approval
    - Monitor for any issues after merge
    - Update Trello card status

## Development Standards

### Branching Strategy

**Trunk-based Development Principles:**
- ✅ **Short-lived branches** - Feature branches should live for days, not weeks
- ✅ **Simple naming** - Use descriptive but brief names (e.g., `user-profile-fix`)
- ✅ **Single focus** - One feature or fix per branch
- ✅ **Feature flags** - Hide incomplete features behind flags, remove when complete
- ✅ **Stay current** - Regularly pull from `main` to avoid conflicts

> [!TIP]
> **Incomplete iteration > prolonged development**: Ship early iterations behind feature flags to get feedback and adapt to changing requirements.

### Commit Messages

**Semantic Commit Format:**
```
type: Brief description of change

Examples:
fix: Fix steps syncing bug
feat: Add native health data integration
docs: Update API documentation
test: Add unit tests for challenge logic
```

**Commit Types:**
- `fix` - Bug fixes and most changes (99% of commits are this)
- `feat` - New native/breaking changes (e.g., native dependency upgrades)
- `docs` - Documentation updates
- `test` - Adding or modifying tests
- `ci` - CI/CD configuration changes
- `chore` - Maintenance tasks

> [!NOTE]
> **Commit types and versioning**: Your commit types help determine version bumps during deployment. See the [Version Management section](./DEPLOYMENT.md#version-management) in the Deployment Guide for how commit types relate to semantic versioning.

### Pull Request Process

1. **Create PR from feature branch to `main`**
    ```bash
    # Ensure your branch is up to date
    git checkout main && git pull
    git checkout your-branch
    git rebase main
    ```

1. **Complete PR template**
    - Clear description of changes
    - Screenshots/videos for UI changes
    - Reference to Trello card
    - Testing notes

1. **Request appropriate reviewers**
    - Frontend changes → Frontend developer
    - Backend changes → Backend developer
    - Cross-cutting changes → Lead developer

1. **Address review feedback**
    - Respond to all comments
    - Make requested changes
    - Re-request review when ready

### Code Review Guidelines

**Review Checklist:**
- [ ] **Code Quality**: Follows our coding standards and patterns
- [ ] **Testing**: Adequate test coverage and edge cases considered
- [ ] **Performance**: Efficient implementation without obvious bottlenecks
- [ ] **Maintainability**: Clear, readable code with appropriate comments
- [ ] **Security**: No obvious security vulnerabilities
- [ ] **Documentation**: Updated if changes affect public APIs

**Review Philosophy:**
- Focus on **code quality** over personal preferences
- Suggest **improvements** rather than just pointing out problems
- **Explain reasoning** behind feedback
- **Approve quickly** when standards are met

## Maintenance & Updates

### Package Updates

**Minor patch updates** (safe, automated):
```bash
npm update                 # Update to latest patch versions
npx expo install --fix     # Sync Expo dependencies
```

**Minor version updates** (review changes):
```bash
npx npm-check -u          # Interactive update tool
```

**Major Expo SDK upgrades** (requires testing):
```bash
npm i -g eas-cli                    # Update EAS CLI
npm install expo@^52.0.0           # Update to new Expo version
npx expo install --fix             # Sync all Expo dependencies
npx expo-doctor                    # Validate configuration
```

> [!WARNING]
> **Test thoroughly after major updates**: Major version changes can introduce breaking changes. Test on both platforms before merging. NEVER ONCE has a major version update not required significant changes to our codebase... 😥

## Debugging & Troubleshooting

### Android Debugging

**Common Android commands:**
```bash
# Uninstall app from device/emulator
adb uninstall com.flybodies.flyfitdev

# Reset app permissions
adb shell pm reset-permissions com.flybodies.flyfitdev

# Start emulator in background
nohup emulator -avd Pixel_9_API_35 &
```

### Performance Profiling

**Flashlight** - Android performance profiling tool:

1. **Install Flashlight**
    ```bash
    curl https://get.flashlight.dev | bash
    ```

1. **Start profiling session**
    ```bash
    flashlight measure
    ```

1. **View results**
    - Open http://localhost:3000/ in browser
    - Connect Android device via USB
    - Click "Start Measuring" to profile active app

### Common Issues & Solutions

**Build failures:**
- Clean and rebuild: `npm run clean && npm run ios:dev`
- Check Xcode/Android Studio versions
- Verify Node.js version: `node -v` (should be 22.x)

**Metro bundler issues:**
- Clear cache: `npm start -- --clear-cache`
- Reset Metro: `npx react-native start --reset-cache`

**Simulator/Emulator problems:**
- Restart simulators
- Check available disk space
- Update simulator OS versions

## Next Steps

### 🎯 Ready to Contribute

**Start developing:**
1. Pick up a task from [Trello](https://trello.com/b/Z6sPJTtg/fly-fit-kanban-board)
2. Follow the [Feature Development Workflow](#feature-development-workflow)
3. Create your first pull request

### 📚 Learn More

**Deepen your understanding:**
- [**Architecture Guide**](./ARCHITECTURE.md) - Technical patterns and system design
- [**Operations Guide**](./OPERATIONS.md) - Project resources and monitoring
- [**Deployment Guide**](./DEPLOYMENT.md) - Release and deployment process
