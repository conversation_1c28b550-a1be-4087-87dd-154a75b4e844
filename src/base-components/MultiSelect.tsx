import {useRef, useState} from 'react';
import {Chip} from 'react-native-paper';
import {MultiSelect as MultiSelectInternal} from 'react-native-element-dropdown';
import type {IMultiSelectRef} from 'react-native-element-dropdown';
import {useAppTheme} from '@utils';
import {Box} from './Box';
import {Text} from './Text';
import {TextInputPaper} from './TextInput';

type SelectItem = {
  label: string;
  value: string;
};

type MultiSelectProps = Pick<React.ComponentProps<typeof MultiSelectInternal>, 'value' | 'onChange' | 'placeholder'> & {
  isDisabled?: boolean | undefined;
  isSearch?: boolean | undefined;
  options: SelectItem[];
};

export const MultiSelect: React.FC<MultiSelectProps> = props => {
  const [isFocused, setIsFocused] = useState(false);
  const theme = useAppTheme();
  const renderItem = (item: SelectItem) => (
    <Box style={{
      padding: 17,
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center'
    }}
    >
      <Text style={{flex: 1}}>{item.label}</Text>
      {!!item.value && !!props.value?.find(_ => _ === item.value) && (
        <Text mr={1}>✅</Text>
      )}
    </Box>
  );
  const ref = useRef<IMultiSelectRef | null>(null);

  return (
    <MultiSelectInternal
      ref={ref}
      data={props.options}
      disable={props.isDisabled ?? false}
      iconStyle={{}}
      labelField='label'
      placeholderStyle={{
        color: theme.colors.secondaryContainer2,
      }}
      renderItem={renderItem}
      renderRightIcon={() => (
        <Box
          style={{position: 'absolute', right: 30, top: -2}}
        >
          <TextInputPaper.Icon
            icon={`arrow-${isFocused ? 'up' : 'down'}-drop-circle-outline`}
            onPress={() => {
              ref.current?.open();
            }}
          />
        </Box>
      )}
      renderSelectedItem={(item: SelectItem, unSelect?: ((item: SelectItem) => void)) => (
        <Chip
          mode='outlined'
          showSelectedCheck={false}
          style={{
            marginRight: 6,
            marginTop: 6,
            paddingLeft: 0,
            backgroundColor: 'transparent',
          }}
          textStyle={{marginHorizontal: 0}}
          onClose={() => unSelect?.(item)}
        >{item.label}
        </Chip>
      )}
      search={props.isSearch ?? false}
      searchPlaceholder='Search...'
      selectedStyle={{
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        borderRadius: 14,
        marginTop: 8,
        marginRight: 12,
        paddingHorizontal: 8,
        paddingVertical: 8,
      }}
      selectedTextStyle={{
        color: theme.colors.primary,
      }}
      style={{
        borderRadius: 4,
        paddingVertical: 12,
        paddingHorizontal: 10,
        borderColor: theme.colors.cardBorderColor,
        borderWidth: 1,
      }}
      value={props.value}
      valueField='value'
      onBlur={() => setIsFocused(false)}
      onChange={props.onChange}
      onFocus={() => setIsFocused(true)}
      {...(props.placeholder ? {placeholder: props.placeholder} : {})}
    />
  );
};
