import {useEffect, useState} from 'react';
import {Box, Button, ButtonCentered, Grid, IconButton, Text, TextInput} from '@base-components';
import type {InitializerOnChangeProps, WorkoutLink} from '@types';
import {
  getOrdinal,
  isNonEmptyArray,
  isValidLink,
  useAppTheme,
  uuid,
} from '@utils';

type WorkoutLinksProps = InitializerOnChangeProps<WorkoutLink[] | undefined> & {
  isInError: boolean;
  setIsInError: (value: boolean) => void;
  value: WorkoutLink[] | undefined;
  valuePreview: (value: WorkoutLink) => React.ReactNode;
};

// eslint-disable-next-line max-lines-per-function -- TODO
export const WorkoutLinks: React.FC<WorkoutLinksProps> = ({
  isInError,
  onChange,
  setIsInError,
  value = [],
  valuePreview,
}) => {
  const theme = useAppTheme();
  const [editableLinks, setEditableLinks] = useState<string[]>([]);

  const handleDone = (id: string) => {
    setEditableLinks(p => p.filter(_ => _ !== id));
  };

  const handleEdit = (id: string) => {
    setEditableLinks(p => [...p, id]);
  };

  const handleAdd = () => {
    const id = uuid();
    onChange(prev => {
      handleEdit(id);

      return [...(prev ?? []), {url: '', label: '', id}];
    });
  };

  const handleRemove = (id: string) => {
    onChange(prev => {
      handleDone(id);

      return prev ? prev.filter(_ => _.id !== id) : prev;
    });
  };

  const handleChange = (id: string, newValue: Partial<WorkoutLink>) => {
    onChange(prev => prev?.map(item => (item.id === id ? {...item, ...newValue} : item)));
  };

  useEffect(() => {
    const isError = isNonEmptyArray(editableLinks) || !value.map(l => l.url).every(isValidLink);
    setIsInError(isError);
  }, [value, editableLinks, setIsInError]);

  return (
    <>
      <ButtonCentered boxProps={{justifyContent: 'flex-start'}} icon='link-variant-plus' mode='text' onPress={handleAdd}>
        Add link
      </ButtonCentered>

      {isNonEmptyArray(value) && (
        <>
          {value.map((item, index) => (
            <Box key={`workout-link-${item.id}`} justifyContent='center'>
              {editableLinks.includes(item.id) ? (
                <>
                  <TextInput
                    dense
                    label={`${getOrdinal(index + 1)} link label`}
                    placeholder='Zoom workout link'
                    value={item.label}
                    onChangeText={v => handleChange(item.id, {label: v})}
                  />

                  <TextInput
                    dense
                    error={!isValidLink(item.url)}
                    label={`${getOrdinal(index + 1)} link*`}
                    placeholder='https://zoom.us/j/1234567890'
                    style={{marginTop: 8}}
                    value={item.url}
                    onChangeText={v => handleChange(item.id, {url: v})}
                  />
                  {!isValidLink(item.url) && (
                    <Text style={{color: theme.colors.error, paddingTop: 6}}>
                      Link must be valid URL
                    </Text>
                  )}

                  <Button
                    disabled={!isValidLink(item.url)}
                    icon='check-outline'
                    mode='text'
                    textColor='green'
                    onPress={() => handleDone(item.id)}
                  >
                    Done
                  </Button>
                  <Button
                    icon='link-variant-remove'
                    mode='text'
                    textColor={theme.colors.delete}
                    onPress={() => handleRemove(item.id)}
                  >
                    Remove
                  </Button>
                </>
              ) : (
                <>
                  {valuePreview(item)}
                  <Box flexDirection='row' justifyContent='flex-start'>
                    <IconButton
                      icon='pencil-outline'
                      iconColor={theme.colors.edit}
                      onPress={() => handleEdit(item.id)}
                    />
                    <IconButton
                      icon='link-variant-remove'
                      iconColor={theme.colors.delete}
                      onPress={() => handleRemove(item.id)}
                    />
                  </Box>
                </>
              )}
            </Box>
          ))}
          {isInError && (
            <Grid item pt={1} xs={12}>
              <Text style={{color: theme.colors.error}}>
                All link fields must be saved to create the workout
              </Text>
            </Grid>
          )}
        </>
      )}
    </>
  );
};
