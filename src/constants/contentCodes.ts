/* eslint-disable max-lines  -- content file */
/* eslint-disable sonarjs/no-nested-template-literals -- simplicity */
/* eslint-disable no-console -- can't reference logger in constants file */
import {ChallengeGroupingType, ChallengeType, patternMatch, type PushNotificationChallengeTypes, PushNotificationTypes, WellnessBlogCategories} from '@types';
import {DOMAIN_CONSTANTS} from './domain';
import {APP_NAME, isIos} from './platformProjectEnvironmentConstants';

const SUPPORT_EMAIL = '<EMAIL>';

const STREAK_CONTINUE_MESSAGES: Record<number, string> = {
  1: 'Nice! You started moving today—keep it going tomorrow!',
  2: "You're on a roll! Two days strong—let's make it three!",
  3: "Momentum is building! Three days in a row—you're crushing it!",
  4: "You're making this a habit! Four days in—keep up the great work!",
  5: 'Five days strong! Just two more to complete a full week!',
  6: 'Almost at a full week! Keep the streak alive!',
  7: '🔥 One full week of movement! This is what consistency looks like!',
  8: "You're unstoppable! Over a week strong—what's your next goal?",
  9: 'Your streak is growing—small steps lead to big wins!',
  10: 'Double digits! Ten days straight—how far can you go?',
  11: "You're proving your dedication—another day, another win!",
  12: 'Your consistency is inspiring—keep stacking those days!',
  13: 'Nearly two weeks! Keep this momentum going!',
  14: '💪 Two full weeks! Most people stop by now, but not you!',
  15: "Two weeks and a day—you're building a serious habit!",
  16: "16 days strong! You're setting a new personal standard!",
  17: "You've been moving for 17 days straight—amazing effort!",
  18: 'Your streak is a force to be reckoned with!',
  19: 'Every day you move, you win—stay consistent!',
  20: '20 days in! Your streak is officially elite!',
  21: "Three weeks straight! They say it takes 21 days to build a habit—you're proving them right!",
  22: "At this point, it's just part of your day. Amazing consistency!",
  23: "You've made movement a routine—this is what progress looks like!",
  24: '24 days of effort—your dedication is paying off!',
  25: '25 days! This streak is something special—keep it alive!',
  26: '26 days in—every single day counts!',
  27: '27 days and still moving! Keep showing up!',
  28: "28 days—you're just two days away from a full month!",
  29: 'One more day until a full month—finish strong!',
  30: "🔥🔥 One full month! You're setting the bar high—let's see how far you can go!",
  31: 'Your streak is legendary! Keep showing up, one day at a time!',
};
const PRE_STREAK_MESSAGES: Record<number, string> = {
  0: 'Move today to start your streak!',
  1: 'Keep it going! A quick workout will keep your streak alive!',
  2: "You're building momentum—stay consistent today!",
  3: "Your streak is growing—don't stop now!",
  4: 'Four days strong! Keep the streak alive!',
  5: 'Halfway to a full week—make today count!',
  6: 'Almost a full week—finish strong!',
  7: "🔥 One week of consistency—don't break the chain now!",
  8: "You're proving your commitment—keep the streak alive!",
  9: 'Your dedication is showing—stay active today!',
  10: 'Double digits! Ten days in—keep pushing forward!',
  11: "You're crushing it—one day at a time!",
  12: "Twelve days strong—don't let today be the one you miss!",
  13: "You've come this far—keep the momentum going!",
  14: '💪 Two full weeks of consistency—make today count!',
  15: 'Fifteen days straight! Keep going strong!',
  16: "You're setting a new standard—move today to keep it up!",
  17: "17 days in—you're proving your dedication!",
  18: "Don't stop now! Every day you move, you win!",
  19: "You've made movement a part of your routine—keep it up!",
  20: '20 days of effort—your streak is next-level!',
  21: 'Three weeks of consistency! Move today to stay on track!',
  22: "This streak is becoming legendary—don't break it now!",
  23: "You've put in 23 days—keep showing up!",
  24: 'One step at a time—stay consistent today!',
  25: '25 days strong! Keep your momentum going!',
  26: "You're proving you can do this—move today!",
  27: 'Just three days from a full month—keep going!',
  28: "28 days in—you're unstoppable!",
  29: 'One more day until a full month—finish strong!',
  30: '🔥🔥 One month in—make today another win!',
  31: "Your streak is legendary—don't let today be the day it stops!",
};

const QUIZ_STREAK_CONTINUE = [
  'Brain gains unlocked!',
  "You're on fire, brainiac!",
  'Smarter every tap!',
  'Your brain just leveled up!',
  'Quiz crushed, no survivors!',
];

// eslint-disable-next-line @typescript-eslint/naming-convention, max-lines-per-function, complexity -- constants
export const CONTENT_CODES = () =>
  ({
    AUTH: {
      LOGIN_HEADER_EMAIL: 'Login with your email',
      LOGIN_HEADER_PHONE_NUMBER: 'Login with your phone number',
      SIGN_UP_HEADER: 'Sign up with your email',
      FORGOT_PASS: 'Forgot Password?',
      LOGIN_BUTTON: 'Login',
      SIGN_UP_BUTTON: 'Sign Up',
      NO_ACCOUNT: "Don't have an account?",
      CLICK_TO_SIGN_UP: 'Click here to sign up',
      ALREADY_HAVE_ACCOUNT: 'Already have an account?',
      CLICK_TO_LOGIN: 'Click here to login',
      CREDENTIAL_BOTH_LABEL: 'Email or phone number',
      CREDENTIAL_EMAIL_LABEL: 'Email',
      CREDENTIAL_PHONE_LABEL: 'Phone number',
      PASSWORD_LABEL: 'Password',
      FIRST_NAME_LABEL: 'First Name',
      FIRST_NAME_PLACEHOLDER: 'Austin',
      LAST_NAME_LABEL: 'Last Name',
      LAST_NAME_PLACEHOLDER: 'Ollis',
      EMAIL_PLACEHOLDER: '<EMAIL>',
      PHONE_NUMBER_PLACEHOLDER: '**********',
      EMAIL_OPEN_BUTTON: 'Open Email App',
      PASSWORD_PLACEHOLDER: '',
      LOGIN_MAGIC_LINK:
        'Please check your email for a login link. On your same device, click the link in the email, and you will be be redirected back to the app.',
      PLEASE_WAIT_LOGGING_IN: 'Please wait while we are logging you in...',
      CHECK_TEXTS_FOR_CODE: 'Check your texts for a 6-digit code.',
      CONFIRM_CODE: 'Confirm Code',
      RESEND_CODE: 'Resend Code',
      SIGN_UP_SUCCESS: 'Sign up successful! 🎉',
      SIGN_UP_MAGIC_LINK:
        'To verify your account and prevent spam, please check your email for a sign in link to continue in the app.',
      SIGN_UP_ERROR: 'There was an error creating your account, please try again',
    },
    ERRORS: {
      GENERIC_LOGIN: 'There was an error logging in',
    },
    PRIVACY_POLICY_CONFIRMATION: {
      TITLE: 'Privacy Policy Agreement',
      WELCOME: 'Welcome to FlyFit!',
      BODY_1:
        'At Fly Bodies, we take protecting your personal data seriously. This policy outlines how we collect, use, and protect your personal information. Understanding and consenting to our privacy practices ensures you understand how we use and protect your data.',
      BODY_2:
        'Please take a moment to review the following privacy policy. By clicking "Accept", you acknowledge that you have read, understood, and accepted the terms. If you have any questions or concerns, please feel free to contact at ',
      CONTACT_EMAIL: SUPPORT_EMAIL,
      VIEW_PRIVACY_POLICY_BUTTON_LABEL: 'View Privacy Policy',
      NOT_AGREE: 'If you do not agree to these terms, you will not be able to use FlyFit.',
      CANCEL_BUTTON_LABEL: 'Cancel',
      ACCEPT_BUTTON_LABEL: 'Accept',
    },
    PERMISSIONS: {
      SYNC: {
        TITLE: (type: string = 'Data') => `Health Sync ${type}`,
        DESCRIPTION_1: (type?: string) =>
          `To automatically sync your${type ? ` ${type}` : ''} data from ${isIos ? 'Apple Health' : 'Google Health Connect'}, you must give ${APP_NAME} permission to do so.`,
        DESCRIPTION_2: `${
          isIos ? 'Tap "Turn On All"' : `Tap on "${APP_NAME}" -> "Allow all"`
        } to allow ${APP_NAME} to sync your data`,
        DESCRIPTION_WARNING:
          'Note: if you do not enable these permissions, you will *not* see your data in the app.',
        CONTINUE: 'Continue',
        CANCEL: 'Cancel',
        OPEN_HEALTH_APP: isIos ? 'Open Apple Health' : 'Open Health Connect',
        WARNING_HEADER: 'Sync Not Enabled',
        WARNING_DESCRIPTION_1:
          'You do not have all health sync permissions enabled. Your data will not synchronize or show in the app.',
        WARNING_DESCRIPTION_2: 'To enable these permissions, you will have to:',
        WARNING_DIRECTIONS_1: isIos
          ? '1. Open up the Apple Health app'
          : '1. Open up the Google Health Connect app',
        WARNING_DIRECTIONS_2: isIos ? '2. Tap on your profile' : '2. Tap on "App permissions"',
        WARNING_DIRECTIONS_3: isIos
          ? '3. Under the Privacy section, tap on "Apps"'
          : `3. Under "Not allowed access", tap on ${APP_NAME}`,
        WARNING_DIRECTIONS_4: isIos
          ? `4. Tap on ${APP_NAME}`
          : '4. Toggle the individual health permissions for reading and writing',
        WARNING_DIRECTIONS_5: isIos ? '5. Tap "Turn On All"' : '5. Navigate back to the app',
      },
    },
    HOME: {
      NO_WORKOUTS: 'No workouts from this day',
      SYNC: 'Sync',
      CALENDAR: {
        START_STREAK: 'Log a workout or meet a step/mileage goal to start a streak.',
        CONTINUE_STREAK:
          "You're on fire! Log a workout or meet a step/mileage goal today to continue the streak!",
        MOVEMENT_STREAK_LABEL: 'Current Movement Streak',
        STREAK_DAYS_LABEL: (hasFire: boolean | undefined) => `days${hasFire ? ' 🔥' : ''}`,
        GOAL_LABEL: 'Goal:',
        MILES_UNIT_LABEL: 'miles',
        STEPS_UNIT_LABEL: 'steps',
        PERCENT_COMPLETE_UNIT_LABEL: '% complete',
        MILEAGE_LABEL: 'Mileage',
        STEPS_LABEL: 'Steps',
      },
    },
    STREAK: {
      STREAK_COMPLETE_TODAY: (streakNumber: number) => {
        if (streakNumber === 0) {
          console.error('streakNumber is 0 when expected >0');
        }
        return STREAK_CONTINUE_MESSAGES[streakNumber] || STREAK_CONTINUE_MESSAGES[31];
      },
      STREAK_NOT_YET_COMPLETE_TODAY: (streakNumber: number) =>
        PRE_STREAK_MESSAGES[streakNumber] || PRE_STREAK_MESSAGES[31],
      EXPLANATION_MODAL: {
        TITLE: 'Movement Streak',
        WHAT_IS_STREAK: 'What is a movement streak?',
      },
    },
    EXPLANATIONS: {
      STREAK: {
        TITLE: 'Congratulations on your first streak! 🎉',
        CONTENT_1:
          'A streak starts when you log a workout and/or meet a step/mileage goal. Keep it up to see how high of a streak you can set! 🚀',
        CONTENT_2: "...and don't worry, you can always start a new streak if you miss a day!",
        OK_BUTTON: 'Got it',
      },
      ENABLE_NOTIFICATIONS_MODAL: {
        TITLE: 'Never miss an update',
        CONTENT_1: 'Enable notifications to be notified about your workouts and challenges.',
        OK_BUTTON: 'Go to notifications',
        CANCEL: 'Not now',
      },
      ENABLE_CHALLENGE_NOTIFICATIONS_MODAL: {
        TITLE: 'Welcome to the Challenges tab!',
        CONTENT_1:
          'On this tab, you will find challenges that you are invited to and participating in.',
        CONTENT_2:
          'To be notified about challenge milestones (i.e. start and end of challenge), please enable challenge push notifications. You can disable them at any time',
        CONFIRM_BUTTON: 'Enable Challenge Notifications',
        CANCEL: 'Maybe later',
      },
      TRACKING_DEVICE_POP_UP: {
        HEADER: 'Step Tracking',
        START_DESCRIPTION_1:
          'FlyFit requires you to track your steps externally via a wearable, tracking app, or phone.',
        START_DESCRIPTION_2:
          'The following questions are asked so that we can help you get your steps and mileage showing in FlyFit.',
        ASH_WHICH_TRACKING_DEVICE: 'What device or wearable are you using to track your steps?',
        ASK_WHICH_TRACKING_APP: 'What app are you using to track your steps?',
        TRACKING_APP_LABEL: 'Tracking app name',
        TRACKING_APP_PLACEHOLDER: 'MyFitnessApp',
        ALL_SET: 'You are all set! 🎉',
        ALL_SET_EXPLANATION:
          'You can change your tracking device settings in the Health Sync Settings at any time.',
        RELOADING_HEALTH_DATA: 'Reloading health data...',
        CANCEL: 'Cancel',
        CONTINUE: 'Continue',
        CLOSE: 'Close',
      },
    },
    EDIT_USER: {
      CREATE_HEADER: 'Create User',
      EDIT_HEADER: 'Edit User',
      FIRST_NAME_LABEL: 'First Name*',
      FIRST_NAME_PLACEHOLDER: 'Austin',
      FIRST_NAME_ERROR: 'First name is required',
      LAST_NAME_LABEL: 'Last Name*',
      LAST_NAME_PLACEHOLDER: 'Ollis',
      LAST_NAME_ERROR: 'Last name is required',
      EMAIL_LABEL: 'Email*',
      EMAIL_PLACEHOLDER: '<EMAIL>',
      PHONE_LABEL: 'Phone Number (optional)',
      PHONE_PLACEHOLDER: '**********',
      PROFILE_PICTURE_LABEL: 'Profile Picture',
      PROFILE_PICTURE_PLACEHOLDER: 'https://i.imgur.com/eCqOlz6.png',
      PROFILE_PICTURE_PREVIEW_LABEL: 'Preview Profile Picture',
      PRIVATE_PROFILE_LABEL: 'Is profile private',
      MILEAGE_SOURCE_LABEL: 'Source mileage from step data?',
      SUBMIT_SUCCESS: 'Profile saved successfully! ✅',
      STEP_LENGTH_LABEL: 'Step Length (inches)',
      TYPE_LABEL: 'User Type',
      TEACHER_TYPE_LABEL: 'Teacher',
      TRAINER_TYPE_LABEL: 'Trainer',
      COACH_TYPE_LABEL: 'Coach',
      STUDENT_TYPE_LABEL: 'Student',
      CLIENT_TYPE_LABEL: 'Client',
      ADMIN_TYPE_LABEL: 'Admin',
      SUBMIT_CREATE_LABEL: 'Create User',
      SUBMIT_UPDATE_LABEL: 'Save User',
      SUBMIT_UPDATE_PROFILE_LABEL: 'Save Profile',
      EDIT_FIRST_NAME_LABEL: 'First Name',
      EDIT_LAST_NAME_LABEL: 'Last Name',
      EDIT_EMAIL_LABEL: 'Email',
      EDIT_PROFILE_PICTURE_LABEL: 'Profile Picture',
      PROFILE_PICTURE_UPLOAD_BUTTON: 'Select Picture',
      SEARCH_ORGANIZATIONS_LABEL: 'Add user to organization',
      RESET_BUTTON: 'Reset',
      DELETE_USER_LABEL: 'Delete User',
      SEND_WELCOME_EMAIL: 'Send Welcome Email',
      ALERT_DELETE: {
        HEADER: 'Delete User',
        BODY: 'Are you sure you want to delete this user? This action is PERMANENT and cannot be undone.',
      },
      HEALTH_PROFILE_LABEL: 'Health Profile',
      GOALS_MODAL: {
        TITLE: 'Personalize Your Goals',
        EDIT_LATER_MSG:
          'You can edit your goals at any time on the home screen or in your profile settings.',
        SUBTITLE: 'Set your daily mileage and step goal for the challenge!',
        SAVE_BUTTON: 'Commit to Goals',
        EXPLANATION_BUTTON: 'ℹ️ What should my step/mileage goal be?',
        EXPLANATION_1: "Let's Look at the Research",
        EXPLANATION_2:
          'Several studies suggest that with more steps per day, all-cause mortality risk (risk of  death from any cause) progressively decreases before leveling off at:',
        EXPLANATION_3: ' • 7,000-10,000 steps per day for adults aged 18-59 years',
        EXPLANATION_4: ' • 6,000-8,000 steps per day for adults aged 60+ years',
        EXPLANATION_5: ' • 7,500 steps per day for women aged 62-101 years',
        EXPLANATION_6:
          'Though there is some variability between studies, there is a good amount of consistency to indicate that there may be a "sweet spot" for longevity somewhere between 6,000 - 10,000 steps depending on your age.',
        EXPLANATION_CONTINUE: 'Continue',
        CONFIRMATION_CHALLENGE: 'AWESOME! You have committed to your goals for the challenge! 🥳',
        CONFIRMATION_STANDARD: 'AWESOME! You have set your mileage and step goals! 🥳',
        CONFIRMATION_MOVEMENT_STREAK:
          'Note: If you hit your goal, you will start a movement streak.',
        CONFIRMATION_CONTINUE_BUTTON: "Let's move!",
      },
    },
    WORKOUT: {
      CREATE_WORKOUT_SELF_HEADER: 'Create Workout',
      CREATE_WORKOUT_TRAINER_HEADER: 'Create Workout for Clients',
      EDIT_HEADER: 'Edit Workout',
      NAME_LABEL: 'Workout Name',
      NAME_PLACEHOLDER: 'Bronson Caves Hike',
      DATE_LABEL: 'Date',
      TIME_LABEL: 'Time',
      DURATION_LABEL: 'Duration',
      DAYS_IN_FUTURE: 'Days in the future',
      NOTES_LABEL: 'Notes',
      NOTES_PLACEHOLDER: '3 mile hike at Bronson Caves in Griffith Park',
      PARTICIPANTS_LABEL: 'Add Workout Participants',
      TYPE_LABEL: 'Workout Type(s)',
      TYPE_INVALID_LABEL: 'You must select at least one workout type',
      RESET_LABEL: 'Reset',
      SUBMIT_SUCCESS: 'Successfully submitted workout! ✅',
      SUBMIT_BUTTON: 'Create Workout',
      CREATE_WORKOUT_BUTTON: 'Log Workout',
      VIEW_AND_EDIT_WORKOUT_HEADER: 'View & Edit Workout',
      OVERRIDE_DATE_ERROR_LABEL: 'Check the date and time on a duplicated workout',
      OVERRIDE_PARTICIPANT_ERROR_LABEL: 'Confirm participants for workout',
      DUPLICATE_BUTTON: 'Duplicate',
      EDIT_WORKOUT_BUTTON: 'Edit Workout',
      SAVE_WORKOUT: 'Save Workout',
      ACTION_SHEET: {
        DELETE: 'Delete Workout',
        CANCEL: 'Cancel',
        CREATE_WORKOUT_SELF: 'Create workout (self)',
        CREATE_WORKOUT_TRAINER: 'Create workout (trainer)',
      },
      ALERT_DELETE: {
        HEADER: 'Delete Workout',
        BODY: 'Are you sure you want to delete this workout? This action cannot be undone.',
      },
    },
    MANUAL_ENTRY: {
      ADD_ENTRY_BUTTON: 'Edit Steps',
      EDIT_ENTRY_BUTTON: 'Edit Steps',
      SCREEN_TITLE: 'Edit Steps',
      ACTION_SHEET: {
        CREATE_MANUAL_ENTRY_SELF: 'Create manual entry (self)',
        CANCEL: 'Cancel',
      },
    },
    MEAL: {
      CREATE_MEAL: 'Log Meal',
      CREATE_SELF_MEAL_HEADER: 'Log Meal',
      CREATE_TRAINER_MEAL_HEADER: 'Log Meal for Clients',
      EDIT_MEAL: 'View & Edit Meal',
      NAME_LABEL: 'Meal Name',
      NAME_PLACEHOLDER: 'Breakfast',
      DATE_LABEL: 'Date',
      TIME_LABEL: 'Time',
      NOTES_HELPER_TEXT: 'Document all of your meal details here.',
      NOTES_LABEL: 'Notes',
      NOTES_PLACEHOLDER: '2 eggs, 1 cup of oatmeal',
      SUBMIT_MEAL: 'Log Meal',
      SAVE_MEAL: 'Save Meal',
      PARTICIPANTS_LABEL: 'Add Meal Participants',
      DUPLICATE_BUTTON: 'Duplicate',
      ACTION_SHEET: {
        TAKE_PHOTO: 'Take photo of meal',
        UPLOAD_PHOTO: 'Upload photo of meal',
        CREATE_MEAL_SELF: 'Create meal (self)',
        CREATE_MEAL_TRAINER: 'Create meal (trainer)',
        CANCEL: 'Cancel',
        DELETE: 'Delete Meal',
      },
      ALERT_DELETE: {
        HEADER: 'Delete Meal',
        BODY: 'Are you sure you want to delete this meal? This action cannot be undone.',
      },
    },
    ORGANIZATION: {
      CREATE_HEADER: 'Create Organization',
      VIEW_AND_EDIT_HEADER: 'View & Edit Organization',
      SEARCH_ADMIN_USERS_LABEL: 'Add Organization Admins',
      SEARCH_ADMIN_SELECTED_LABEL: 'Selected Admins',
      SEARCH_ORGANIZATIONS_LABEL: 'Search Organizations',
      SEARCH_ORGANIZATIONS_PLACEHOLDER: 'Search by organization name',
      SEARCH_ADD_ORGANIZATION_LABEL: 'Add',
      SELECTED_ORGANIZATIONS_LABEL: 'Selected Organizations',
      SELECT_PARENT_ORGANIZATIONS_LABEL: 'Select Parent Organization (optional)',
      SEARCH_SELECTED_PARENT_ORGANIZATION: 'Selected Parent Organization',
      NAME_LABEL: 'Organization Name',
      NAME_ERROR_LABEL: 'Organization name is required',
      NAME_PLACEHOLDER: 'Fly Bodies',
      TYPE_LABEL: 'Organization Type',
      ADMINS_LABEL: 'Organization Admins',
      SEARCH_COACH_USERS_LABEL: 'Organization Trainers/Coaches',
      SEARCH_COACH_SELECTED_LABEL: 'Selected Trainers/Coaches',
      SEARCH_CLIENT_USERS_LABEL: 'Organization Clients',
      SEARCH_CLIENT_SELECTED_LABEL: 'Selected Clients',
      SAVE_BUTTON: 'Save Organization',
      CREATE_BUTTON: 'Create Organization',
      ACTION_SHEET: {
        DELETE: 'Delete Organization',
        CANCEL: 'Cancel',
      },
      ALERT_DELETE: {
        HEADER: 'Delete Organization',
        BODY: 'Are you sure you want to delete this organization? This action cannot be undone.',
      },
      DELETE_ACTION_SHEET_HEADER: 'Delete Organization',
      DELETE_ACTION_SHEET_BODY:
        'Are you sure you want to delete this organization? This action cannot be undone.',
    },
    ADMIN: {
      HEADER_TITLE: 'Admin Home',
      ORGANIZATIONS_LABEL: 'Organizations',
      CREATE_ORGANIZATION_LABEL: 'Create Org',
      USERS_LABEL: 'Users',
      CREATE_USER_LABEL: 'Create User',
    },
    USERS: {
      MIN_SEARCH_LENGTH: `Type in at least ${DOMAIN_CONSTANTS().SEARCH.MIN_SEARCH_LENGTH} character to search`,
      NO_SEARCH_USERS: 'No users found searching',
      SEARCH_PLACEHOLDER: 'Search by name or email',
      SELECTED_PARTICIPANTS_LABEL: 'Selected Users',
      MUST_SELECT_PARTICIPANTS: 'You must add at least one user',
      ADD_LABEL: 'Add',
      REMOVE_LABEL: 'Remove',
    },
    CHALLENGE: {
      CHALLENGES_HEADER_TITLE: 'Challenges',
      CREATE_CHALLENGE_HEADER: (type: ChallengeGroupingType) => {
        const MAPPING = {
          [ChallengeGroupingType.INDIVIDUAL]: 'Create Individual Challenge',
          [ChallengeGroupingType.TEAMS]: 'Create Teams Challenge',
          [ChallengeGroupingType.GROUPS]: 'Create Groups Challenge',
        };
        return MAPPING[type];
      },
      CREATE_CHALLENGE_BUTTON: 'Create Challenge',
      MY_CHALLENGES_HEADER_TITLE: 'My Challenges',
      MY_PENDING_CHALLENGES_HEADER: 'My Pending Challenges',
      MY_INACTIVE_CHALLENGES_HEADER: 'My Inactive Challenges',
      NO_EDITOR_ACTIVE_CHALLENGES: 'You are not managing any active challenges',
      NO_ACTIVE_CHALLENGES: 'You are not in any active challenges',
      SAVE_BUTTON: 'Save Challenge',
      INVITE: {
        HEADER: 'Join Challenge',
        ACCEPT: 'Accept',
        REJECT: 'No thanks',
      },
      ADMIN_SCREEN: {
        HEADER: 'Challenge Admin Screen',
      },
      EDIT: {
        HEADER: 'Edit Challenge',
        CHALLENGE_NAME_LABEL: 'Challenge Name',
        CHALLENGE_NAME_PLACEHOLDER: 'Fall 2023 Mileage Challenge',
        PARTICIPANTS_LABEL: 'Add Challenge Participants',
        TYPE_LABEL: 'Challenge Type',
        START_DATE_LABEL: 'Start Date',
        START_TIME_LABEL: 'Start Time',
        END_DATE_LABEL: 'End Date',
        END_TIME_LABEL: 'End Time',
        DURATION_LABEL: (duration: string) => `Challenge Duration: ${duration}`,
        DURRATION_ERROR: 'Challenge must have a duration',
        MANAGE_TEAMS_LABEL: 'Manage Teams',
        TEAM_NAME_LABEL: 'Team Name',
        TEAM_NAME_PLACEHOLDER: 'Team 1',
        TEAM_PARTICIPANTS_LABEL: 'Manage Team Members',
        TEAM_PICTURE_LABEL: 'Modify Team Picture',
        BANNER: 'Modify Challenge Banner',
        DISMISS_TEAM: 'Save Team',
        CREATE_TEAM: 'Create Team',
        MANAGE_STAGES: 'Manage Stages',
        INVALID_STAGES: 'Invalid stage date ranges, please adjust',
        EDIT_STAGE: 'Edit Stage',
        ADD_STAGE: 'Add Stage',
        START_DATE_STAGE: 'Stage Start Date',
        END_DATE_STAGE: 'Stage End Date',
        STAGE_NAME_LABEL: 'Stage Name',
        STAGE_NAME_PLACEHOLDER: 'Stage 1',
        MANAGE_LEVELS: 'Manage Levels',
        ADD_LEVEL: 'Add Level',
        EDIT_LEVEL: 'Edit Level',
        REMOVE_LEVEL: 'Remove Level',
        LEVEL_LABEL: 'Level Label',
        LEVEL_LABEL_PLACEHOLDER: 'Directorates',
        LEVEL_GROUP_LABEL: 'Level Group Label',
        LEVEL_GROUP_LABEL_PLACEHOLDER: 'Directorate',
        IS_FEED_ENABLED: 'Is feed enabled?',
        IS_DRAFT_STATE: 'Is in draft state?',
        ARE_CHALLENGE_ROLES_ENABLED: 'Are challenge roles enabled?',
        MAX_TEAM_SIZE_LABEL: 'Max team size',
        AGGREGATION_CONFIG_LABEL: 'Aggregation Config',
        AGGREGATION_MODE: 'Aggregation Mode',
        AGGREGATION_INTERVAL: 'Aggregation interval',
        LEVEL_DESCRIPTION: 'Level Description',
        SAVE_LEVEL: 'Save Level',
        EDIT_GROUP: 'Edit Group',
        EDIT_GROUP_DELETE: 'Delete Group',
        EDIT_GROUP_NAME: 'Group Name',
        IS_DAILY_DATA_ENABLED: 'Is showing daily data enabled?',
        EDIT_GROUP_SAVE: 'Save Group',
        GROUP_LAST_AGGREGATION_RUN: 'Last group aggregation run',
        GROUP_LEVEL: 'Group Level',
        STAGE_CLOSED: 'Close',
        SUBMIT_BUTTON: 'Post Challenge',
        SUBMIT_SUCCESS: 'Successfully submitted challenge! ✅',
        NO_CHANGES: 'No changes made',
      },
      VIEW: {
        DEFAULT_HEADER: 'View Challenge',
        LOADING: 'Loading challenge...',
        TYPE_LABELS: {
          MILEAGE: 'Mileage Challenge',
          STEP: 'Step Challenge',
        },
        PROGRESS_HEADER: 'Challenge Progress',
        LEADERBOARD_HEADER: 'Leaderboard',
        STATS: {
          HEADER: 'Challenge Stats',
          HEADER_TEAM: 'Team Stats',
          TOTAL_LABEL: (type: ChallengeType) =>
            patternMatch(type)
              .with(ChallengeType.DISTANCE, () => 'Total cumulative mileage')
              .with(ChallengeType.STEP, () => 'Total cumulative steps')
              .otherwise(() => {
                throw new Error(`Challenge type=${type} not supported`);
              }),
          AVG_LABEL: (type: ChallengeType) =>
            patternMatch(type)
              .with(ChallengeType.DISTANCE, () => 'Average mileage/person')
              .with(ChallengeType.STEP, () => 'Average steps/person')
              .otherwise(() => {
                throw new Error(`Challenge type=${type} not supported`);
              }),
          NUM_PARTICIPANTS: 'Active participants',
          DURATION: 'Duration',
        },
        COUNTDOWN: {
          SINCE_ENDED: 'Time Since Ended',
          SINCE_STARTED: 'Time Remaining',
          UNTIL_STARTED: 'Time Until Start',
        },
        FEED: {
          TEAM_HEADER: 'Team Feed',
          ALL_TEAMS_HEADER: 'All Teams Feed',
          ALL_GROUPS_HEADER: 'All Groups Feed',
          GROUP_HEADER: 'Group Feed',
        },
        TEAMS: {
          NO_TEAM_SELECTED: 'No team selected',
          ALL_TEAMS_SELECTED: 'All teams selected',
          TRAINER_MESSAGE_PLACEHOLDER: 'Write a trainer message to send to team(s)',
          DAILY_TEAM_GOAL: 'Daily Team Goal',
          DAILY_TOTAL: 'Daily Total',
          DAILY_AVERGAGE_LABEL: (type: ChallengeType) =>
            patternMatch(type)
              .with(ChallengeType.DISTANCE, () => 'Daily Average Mileage')
              .with(ChallengeType.STEP, () => 'Daily Average Steps')
              .otherwise(() => {
                throw new Error(`Challenge type=${type} not supported`);
              }),
          DAY_WITH_MOST_LABEL: (type: ChallengeType) =>
            patternMatch(type)
              .with(ChallengeType.DISTANCE, () => 'Day with Most Mileage')
              .with(ChallengeType.STEP, () => 'Day with Most Steps')
              .otherwise(() => {
                throw new Error(`Challenge type=${type} not supported`);
              }),
        },
        GROUPS: {
          NO_GROUP_SELECTED: 'No group selected',
          NO_LEVEL_SELECTED: 'No level selected',
          NO_GROUPS_IN_LIST: 'No groups in list',
          ALL_GROUPS_SELECTED: 'All groups selected',
          TRAINER_MESSAGE_PLACEHOLDER: 'Write a trainer message to send to group(s)',
        },
        PARTICIPANT_CARD: {
          DAILY_LABEL: 'Daily: ',
          TOTAL_LABEL: 'Total: ',
        },
      },
      ALERT_DELETE: {
        HEADER: 'Delete Challenge',
        BODY: 'Are you sure you want to delete this challenge? This action is PERMANENT and cannot be undone.',
        GROUPS_HEADER: 'Delete Challenge Group',
        GROUPS_BODY:
          'Are you sure you want to delete this challenge group? This action is PERMANENT and cannot be undone.',
      },
      SEARCH: {
        PLACEHOLDER: 'Search challenges by name',
        ADD_LABEL: 'Add',
        REMOVE_LABEL: 'Remove',
        SELECTED_CHALLENGES: 'Selected challenge',
      },
      ENCOURAGEMENT: {
        NOT_STARTED: 'Ready to conquer this challenge?',
        BETWEEN_0_10:
          'Just getting started! Every step forward is a step towards your goal. Keep it up!',
        BETWEEN_10_20:
          'Gaining momentum! Remember, the journey of a thousand miles begins with a single step.',
        BETWEEN_20_30:
          "You're off to a great start! Your strength lies in your determination. Stay strong!",
        BETWEEN_30_42:
          'Making significant progress! Every step you take builds the path to your success. Keep up the good work!',
        BETWEEN_42_48:
          'Almost halfway there! Each step is one more step towards your challenge. Push through!',
        BETWEEN_48_52:
          "Halfway mark! Be proud of how far you've come. Can you do even better on the second half of the challenge?",
        BETWEEN_52_60:
          'Beyond halfway! Your commitment shines through every step you take. Keep moving!',
        BETWEEN_60_70:
          "You're conquering this challenge! Consistency and perseverance is your power. Don't let up now!",
        BETWEEN_70_80:
          "Closing in! Your hard work is paying off. You're stronger and healthier with every step.",
        BETWEEN_80_90:
          'So close to completion! Your dedication is inspiring. Finish strong and keep moving!',
        BETWEEN_90_100: "Final stretch - you're almost finished! Make the final steps count.",
        COMPLETE_100:
          'Congratulations on finishing the mileage challenge! Your determination and hard work have paid off, and you can be proud of your progress and making it to the end. Well done!',
      },
      ABOUT: {
        HEADER_TITLE: 'About Challenges',
        WHAT_IS_A_CHALLENGE_HEADER: 'What is the Fly Bodies Mileage Challenge?',
        WHAT_IS_A_CHALLENGE_BODY:
          "It's a 30-day team challenge to get moving! You'll join a small team, track your steps or miles, and compete together to reach stage milestones. It's all about teamwork, daily movement, and a little friendly competition—plus there are prizes to keep things fun!",
        HOW_IT_WORKS_HEADER: 'How It Works',
        HOW_IT_WORKS_BODY:
          "🔹 Join a Team: You'll be placed in a team of ~5 people.\n\n🔹 Walk, Jog, or Run: Log your steps or mileage daily using the app or sync a wearable.\n\n🔹 Compete in Stages: The challenge is broken into stages with shared team goals.\n\n🔹 Climb the Leaderboard: Watch your team move up the ranks in real time.\n\n🔹 Win Prizes: Earn rewards for progress, effort, and team spirit along the way!",
        TRACK_PROGRESS_HEADER: 'Track Progress & Cheer on Your Team',
        TRACK_PROGRESS_BODY:
          'Tracking is easy—connect your wearable or log steps in the app. Use the team feed to motivate each other, share updates, and celebrate wins together!',
        WELLNESS_CONTENT_HEADER: 'Weekly Wellness Boosts',
        WELLNESS_CONTENT_BODY:
          "Each week you'll get quick, helpful tips to stay on track—from movement and nutrition to managing stress and boosting energy. All short, smart, and tailored to support your journey.",
        BENEFITS_HEADER: 'Why Join This Challenge?',
        BENEFITS_BODY:
          "This isn't just about steps—it's about connection, momentum, and fun. These challenges help teams bond, boost morale, and make daily movement part of your routine. You'll feel better, move more, and maybe even win something!",
        GET_MOVING_HEADER: 'Get Moving and Win Big!',
        GET_MOVING_BODY:
          "Whether you're walking 1 mile or running 10, this challenge is for everyone. It's your chance to build healthy habits, support your team, and have a blast doing it!",
        SUMMARY_HEADER: 'Challenge Summary',
        SUMMARY_BODY:
          "Fly Bodies Mileage Challenges bring teams together to move more and feel better. By tracking steps or mileage across daily stages, participants stay motivated, engaged, and connected. It's a fun, inclusive way to build wellness and teamwork into every day.",
        FEATURE_TEAMS_TITLE: 'Team Up',
        FEATURE_TEAMS_DESCRIPTION:
          'Join a ~5-person team and reach your goals together through support and collaboration.',
        FEATURE_STAGES_TITLE: 'Stage Progression',
        FEATURE_STAGES_DESCRIPTION:
          'Challenges are broken into stages with milestone goals to keep motivation high.',
        FEATURE_LEADERBOARDS_TITLE: 'Live Rankings',
        FEATURE_LEADERBOARDS_DESCRIPTION:
          "Track your team's position on the leaderboard as you move through each stage.",
        FEATURE_FEEDS_TITLE: 'Team Feeds',
        FEATURE_FEEDS_DESCRIPTION:
          'Stay connected and encourage teammates with messages and updates in your team feed.',
        FEATURE_PRIZES_TITLE: 'Earn Rewards',
        FEATURE_PRIZES_DESCRIPTION:
          'Unlock prizes for participation, progress, and team energy at each stage.',
        FEATURE_TRACKING_TITLE: 'Easy Tracking',
        FEATURE_TRACKING_DESCRIPTION:
          'Sync your favorite fitness wearable for seamless step and mileage tracking.',
      },
      PROGRESS_MODAL: {
        TITLE: 'Challenge Milestone Reached 🎉',
        BODY_DISTANCE: (value: string, name: string) =>
          `You've reached a milestone of ${value} miles on your "${name}" mileage challenge!`,
        BODY_STEPS: (value: string, name: string) =>
          `You've reached a milestone of ${value} steps on your "${name}" step challenge!`,
        BODY_CONTENT:
          'Keep up the amazing work and continue pushing towards your next goal. Every step you take brings you closer to a healthier, stronger you.',
        DISMISS_BUTTON: 'Dismiss',
        CONTINUE_BUTTON: 'Continue',
      },
      ACTION_SHEET: {
        CREATE_INDIVIDUAL: 'Individual',
        CREATE_TEAMS: 'Teams',
        CREATE_GROUPS: 'Groups',
        EDIT: 'Edit Challenge',
        PUBLIC_INVITE: 'Copy Public Invite Link',
        GENERATE_EXPORT: 'Generate Sheets Export',
        LEAVE_CHALLENGE: 'Leave Challenge',
        DELETE: 'Delete Challenge',
        CANCEL: 'Cancel',
        TRAINER_SEND_MESSAGE: 'Send Trainer Message',
        SEND_INVITE_NOTIFICATIONS: 'Send Invite Notifications',
        RECALCULATE_GROUP_DATA: 'Recalculate Group Data',
      },
    },
    VIEW_PROFILE: {
      HEADER: 'View Profile',
      CONTACT_INFO_LABEL: 'Contact Info',
      PROFILE_INFO_LABEL: 'Profile Info',
      EDIT_PROFILE_BUTTON: 'Edit Profile',
      ACTION_SHEET_CANCEL: 'Cancel',
      EDIT_PROFILE_HEADER: 'Edit Profile',
      HEALTH_DATA_LABEL: 'Personal Health Data',
      GOALS_LABEL: 'Goals',
      DISTANCE_SOURCE_LABEL: 'Mileage sourced from distance',
      TRUE_LABEL: 'Yes',
      FALSE_LABEL_DEFAULT: 'No (default)',
      STEP_LENGTH_LABEL: 'Step Length',
      STEP_LENGTH_UNIT_LABEL: (value: number) => `${value} inches`,
      STEP_LENGTH_DEFAULT_UNIT_LABEL: (value: number) => `${value} inches (default)`,
      DELETE: {
        HEADER: 'Delete Account',
        BODY: 'Are you sure you want to delete your account? This action cannot be undone.\n\nAll associated personal, health, challenge, and workout data will be deleted.\n\nPlease type "DELETE" to confirm account deletion.',
        SUCCESS_HEADER: 'Account Successfully Deleted',
        SUCCESS_BODY: 'You have successfully deleted your account. You are now signed out.',
        ERROR_HEADER: 'Error Deleting Account',
        ERROR_BODY:
          'There was an error deleting your account. Please contact support for assistance.',
        UNCONFIRMED_HEADER: 'Account NOT Deleted',
        UNCONFIRMED_BODY: 'You must type "DELETE" exactly to confirm account deletion.',
      },
    },
    MISC: {
      BACK_BUTTON: 'Back',
      HELP_LABEL: 'Help',
      INFO_LABEL: 'Info',
      RELOAD_LABEL: 'Refresh',
      CREATE_LABEL: 'Create',
      PRIVACY_POLICY_LABEL: 'Privacy',
      ADD_LABEL: 'Add',
      HELP_SUPPORT_LABEL: 'Email FlyBodies Support',
      HELP_URL: `mailto:${SUPPORT_EMAIL}?subject=FlyFit%20Help%20Request`,
      FLY_BODIES_WEBSITE: 'https://www.fly-bodies.com',
      ALERT_DELETE: {
        OK: 'OK',
        CANCEL: 'Cancel',
      },
    },
    SETTINGS: {
      HEADER: 'Settings',
      TRAINER_ACTIONS: {
        LABEL: 'Trainer Actions',
        CREATE_NEW_USER: 'Create New User',
      },
      ACCOUNT_ACTIONS: {
        LABEL: 'Account Actions',
        VIEW_PROFILE: 'My Profile',
        DELETE_ACCOUNT: 'Delete Account',
        NOTIFICATION_SETTINGS: 'Notification Settings',
        HEALTH_SYNC_SETTINGS: 'Health Sync Settings',
        SUMMARY_REPORTS: 'Summary Reports',
        LOG_OUT: 'Log Out',
      },
      APP_SETTINGS: {
        LABEL: 'App Settings',
        BACKGROUND_REFRESH: 'Enable Background Refresh',
        CHECK_FOR_UPDATES: 'Check for updates',
        APP_SETTINGS: `${APP_NAME} App Settings`,
        LOG_DEVICE_INFO: 'Log Device Info',
        HEALTH_APP_LINK: isIos ? 'Open Apple Health App' : 'Open Google Health Connect',
        ANDROID_SELECT_DATA_ORIGIN: 'Your Health Connect data source(s): ',
      },
      DEBUG_ACTIONS: {
        LABEL: 'Debug Actions',
        ASYNC_ERROR: 'Throw Async Error',
        RENDER_ERROR: 'Throw Render Error',
        RESET_DEVICE_LOCAL_STORAGE: 'Reset Device Local Storage',
      },
      APP_INFO: {
        LABEL: 'App Info',
        APP_VERSION_LABEL: 'App Version',
        BUILD_VERSION_LABEL: 'Build Version',
        RELEASE_CHANNEL_LABEL: 'Release Channel',
        UPDATE_ID_LABEL: 'Update ID',
        EMBED_LAUNCH_LABEL: 'Embed Launch',
        LEGAL_SECTION_LABEL: 'Legal',
        PRIVACY_POLICY_LABEL: 'Privacy Policy',
        PRIVACY_POLICY_URL: 'https://www.fly-bodies.com/fly-fit-privacy-policy',
        COPYRIGHT_LABEL: '© 2019 Fly Bodies, LLC. All rights reserved.',
      },
      RESOURCES_AND_CONTACT: {
        LABEL: 'Resources and Contact',
        CONTACT_EMAIL: SUPPORT_EMAIL,
        FEEDBACK_APP_LABEL: 'Send App Feedback',
        FEEDBACK_APP_LINK: 'https://forms.gle/Q6c27dMr5qPSYZ4L8',
        FLY_FIT_FAQ_LABEL: 'FlyFit FAQ',
        FLY_FIT_FAQ_LINK: 'https://www.fly-bodies.com/flyfit-faqs',
      },
      HEALTH_SYNC: {
        HEADER: 'Health Sync Settings',
        TRACKING_DEVICE_LABEL: 'Your step tracking device:',
        TRACKING_APP_LABEL: 'Your step tracking app:',
        HAS_ALL_PERMISSIONS: isIos
          ? 'Connected to Apple Health ✅'
          : 'Connected to Google Health Connect ✅',
        HAS_PARTIAL_PERMISSIONS: isIos
          ? 'Not fully connected to Apple Health ❌'
          : 'Not fully connected to Health Connect ❌',
        HAS_NO_PERMISSIONS: isIos
          ? 'Not connected to Apple Health ❌'
          : 'Not connected to Health Connect ❌',
        EDIT_PROFILE_BUTTON: 'Edit Health Profile',
        CONNECTION_LEARN_MORE: (type: string) => `Learn about how ${type} is connected`,
        CONNECTION_TITLE: (type: string) => `${type} Connection`,
        FITBIT_IS_CONNECTED_EXPLANATION: `Your step and mileage data will now come from Fitbit when ${APP_NAME} syncs.`,
        FITBIT_CONNECTED_LABEL: 'Connected ✅',
        FITBIT_NOT_CONNECTED_LABEL: 'Not Connected ❌',
        FITBIT_NOT_CONNECTED_INFO: isIos
          ? 'ℹ️ Fitbit does not sync to Apple Health on iOS devices. You must login with your Fitbit account to sync your Fitbit health data.'
          : 'ℹ️ To connect to Fitbit directly, you must login with your Fitbit account and give access to FlyFit to sync your health data.',
        FITBIT_DATA_SYNC_TITLE: 'Data Synced with Fitbit',
        FITBIT_HOW_SYNC_TITLE: 'How to Sync with Fitbit',
        FITBIT_DATA_SYNC_1: 'Our Fitbit integration always syncs step and mileage data.',
        FITBIT_DATA_SYNC_2:
          'This means both your steps and mileage data are always being sourced from Fitbit, meaning Fitbit is responsible for the accuracy of your data. If you see an inconsistency with your data between your Fitbit wearable and this app, follow along with the next section to sync manually.',
        FITBIT_LEARN_MORE: 'Learn about how to connect Fitbit with FlyFit on our FAQ',
        FITBIT_LEARN_MORE_LINK:
          'https://www.fly-bodies.com/flyfit-faqs?questionId=3a3700b3-3ec2-4310-a4e5-fa232a31fa95&appDefId=14c92d28-031e-7910-c9a8-a670011e062d',
        // FITBIT_DATA_SYNC_3:
        //   'You can read about why Fitbit recommends using step length for tracking distance here.',
        // FITBIT_DATA_SYNC_3_LINK:
        //   'https://dev.fitbit.com/build/reference/web-api/activity-timeseries/get-activity-timeseries-by-date-range/#Distance-pace-and-speed',
        FITBIT_HOW_SYNC_1: `To ensure ${APP_NAME} is showing the same data as your Fitbit wearable, you must follow these steps to ensure the data is the same:`,
        FITBIT_HOW_SYNC_STEP_1:
          '1. Sync your Fitbit wearable with the Fitbit app manually, using the "Sync now" button in the Fitbit app.',
        FITBIT_HOW_SYNC_STEP_2: '2. Open FlyFit and press "Sync" on the home screen.',
        FITBIT_HOW_SYNC_2: `If you do these steps, ${APP_NAME} should be showing your most recent Fitbit data in the app`,
        FITBIT_REMOVE_LABEL: 'Remove Fitbit Connection',
        FITBIT_EMAIL_INFO:
          'Also, we have emailed you some helpful links to reference if you are experiencing any issues seeing your Fitbit data in FlyFit.',
        // FITBIT_ANDROID_INFO:
        //   'You must connect Fitbit to Google Health Connect in your Fitbit app to sync data.',
        // FITBIT_ANDROID_LEARN_MORE:
        //   'Click here how instructions how to connect Fitbit to Health Connect',
        // FITBIT_ANDROID_LEARN_MORE_LINK:
        //   'https://support.google.com/fitbit/answer/14506680?hl=en#zippy=%2Chow-do-i-set-up-health-connect-in-the-fitbit-app%2Cinstructions',
        APPLE_INFO_1:
          'Your steps and mileage data will now be synced from your Apple Watch, connected by Apple Health.',
        APPLE_INFO_2:
          'Apple watches must be configured to share data with Apple Health as a device.',
        APPLE_INFO_EMAIL:
          'Also, we have emailed you some helpful links to reference if you are experiencing any issues seeing your Apple Watch correct data in FlyFit.',
        APPLE_LEARN_MORE: 'Read more how to connect your Apple Watch to FlyFit on our FAQ',
        APPLE_LEARN_MORE_LINK:
          'https://www.fly-bodies.com/flyfit-faqs?questionId=ec5ab211-bdf9-4f52-bd23-82bc3b5fde22&appDefId=14c92d28-031e-7910-c9a8-a670011e062d',
        GARMIN_IOS_INFO_1:
          'Garmin Connect must be connected to Apple Health for your health data to sync properly.',
        GARMIN_IOS_INFO_LINK:
          'https://www.fly-bodies.com/flyfit-faqs?questionId=f6a495aa-3fc8-4d52-b374-65b34e85a020&appDefId=14c92d28-031e-7910-c9a8-a670011e062d',
        GARMIN_IOS_INFO_LINK_LABEL:
          'Read more how to connect Garmin Connect app to FlyFit on our FAQ',
        GARMIN_IOS_INFO_2:
          'Your steps and mileage data will now be synced from Garmin Connect, connected by Apple Health',
        GARMIN_IOS_EMAIL:
          'Also, we have emailed you some helpful links to reference if you are experiencing any issues seeing your data Garmin data in FlyFit.',
        GARMIN_ANDROID_INFO:
          'Garmin Connect does not directly connect to Health Connect on Android devices just yet.',
        GARMIN_ANDROID_INFO_LINK_LABEL: 'Click here to read about users discussing this issue',
        GARMIN_ANDROID_INFO_LINK:
          'https://forums.garmin.com/apps-software/mobile-apps-web/f/garmin-connect-web/354386/garmin-sync-with-google-health-connect',
        GARMIN_ANDROID_ALT_INFO:
          'If you would still like to sync your steps, you can use a separate synchronization app to do so.',
        GARMIN_ANDROID_ALT_LINK_LABEL: 'Click here to get the Health Sync app',
        GARMIN_ANDROID_ALT_LINK:
          'https://play.google.com/store/apps/details?id=nl.appyhapps.healthsync',
        SAMSUNG_HEALTH_INFO: `Samsung Health must be connected to ${isIos ? 'Apple Health' : 'Health Connect'} for your health data to sync properly.`,
        SAMSUNG_HEALTH_LINK_LABEL: `Learn how to connect Samsung Health to ${isIos ? 'Apple Health' : 'Health Connect'}`,
        SAMSUNG_HEALTH_LINK: isIos
          ? 'https://discussions.apple.com/thread/252492079?sortBy=rank'
          : 'https://developer.samsung.com/health/blog/en/health/blog/accessing-samsung-health-data-through-health-connect#Health-Connect',
        WHOOP_INFO: `WHOOP must be connected to ${isIos ? 'Apple Health' : 'Health Connect'} for your health data to sync properly.`,
        WHOOP_LINK_LABEL: `Learn how to connect your WHOOP app to ${isIos ? 'Apple Health' : 'Health Connect'}`,
        WHOOP_LINK: isIos
          ? 'https://support.whoop.com/s/article/Apple-Health-Integration?language=en_US'
          : 'https://support.whoop.com/s/article/Google-Health-Integration-For-Android?language=en_US',
        COROS_INFO: `Coros must be connected to ${isIos ? 'Apple Health' : 'Health Connect'} for your health data to sync properly.`,
        COROS_LINK_LABEL: `Learn how to connect your Coros app to ${isIos ? 'Apple Health' : 'Health Connect'}`,
        COROS_LINK: isIos
          ? 'https://support.coros.com/hc/en-us/articles/360041549551-How-to-connect-Apple-Health-with-the-COROS-app'
          : 'https://support.coros.com/hc/en-us/articles/360040256591-Sync-with-3rd-party-apps',
        OURA_INFO: `Oura must be connected to ${isIos ? 'Apple Health' : 'Health Connect'} for your health data to sync properly.`,
        OURA_INFO_LINK_LABEL: `Learn how to connect your Oura app to ${isIos ? 'Apple Health' : 'Health Connect'}`,
        OURA_INFO_LINK: isIos
          ? 'https://support.ouraring.com/hc/en-us/articles/360025438734-How-to-Use-Apple-Health-with-Oura'
          : 'https://support.ouraring.com/hc/en-us/articles/10786105824531-How-to-Use-Health-Connect-by-Android-with-Oura',
        OTHER_INFO: `If you are using a tracking device or wearable not listed, you will have to search for how to connect your specific device to ${isIos ? 'Apple Health' : 'Health Connect'}.`,
        PHONE_GENERIC_INFO: isIos
          ? 'Since you are choosing to use your iPhone to track your steps, you simply just need to use Apple Health to ensure your steps are being tracked accurately. Remember to walk with your phone for the steps to track.'
          : 'On Android, you MUST use a 3rd party tracking app if you want to use your phone as your step tracker. We recommend Google Fit on Android since it is a free, proven app that will work with any Android device.',
        NO_DEVICE_CONTENT_1: isIos
          ? "If you don't currently have a wearable or tracker device, we recommend simply using your iPhone as your step tracker since it is built in and enabled by default in Apple Health."
          : "If you don't currently have a wearable or tracker device, you can still track your steps from your phone with a 3rd party step tracking app (it is not built into the phone by default). This app must be connected to Health Connect for FlyFit to show your step data.",
        NO_DEVICE_CONTENT_2: isIos
          ? 'If you have connected to Apple Health already, your steps are already being tracked and synced with FlyFit.'
          : 'If you already have a step tracking app installed, we encourage you to use that one (i.e. pre-installed apps like Google Fit, Samsung Health, or Fitbit).',
        NO_DEVICE_CONTENT_3: isIos
          ? 'On the next screen, select *Phone* as your step tracking device.'
          : 'Otherwise, if you are looking for a step tracking app, we recommend Google Fit on Android since it is a free app that will work with any Android device.',
        HEALTH_SYNC_BUTTON_LABEL: 'Health Sync Full Reload',
        HEALTH_SYNC_MODAL_TITLE: 'Health Sync Full Reload',
        HEALTH_SYNC_EXPLANATION_1:
          'Running a health sync full reload will forcibly re-synchronize all of your data from your connected health app.',
        HEALTH_SYNC_EXPLANATION_2:
          'You should do this if you changed wearables, changed a health profile setting such as step length, or if your old data does not look correct.',
        HEALTH_SYNC_EXPLANATION_3: 'This may take a up to 30 seconds.',
        HEALTH_SYNC_BUTTON_CONTINUE: 'Continue with reload',
        HEALTH_SYNC_CANCEL: 'Cancel',
        HEALTH_SYNC_COMPLETE_SNACK: 'Health sync full reload complete ✅',
      },
    },
    ERROR: {
      TITLE: 'Oops, something went wrong',
      DESCRIPTION: "We're sorry, but something went wrong. Please try again.",
      ACTION_ITEM: '',
      RELOAD: 'Reload App',
      THROW_ERROR: 'Throw Error',
    },
    VALIDATION: {
      EMAIL: 'Invalid email address',
    },
    INVITE_CODES: {
      MODAL: {
        HEADER: 'Invite Code',
        LOADING_CODE: 'Validating invite code...',
        ERROR_NOT_FOUND:
          "We couldn't find an invite matching this code. Please double-check the code and try again. If the issue persists, contact support.",
        CLOSE: 'Close',
        CONTINUE: 'Continue',
        CHALLENGE: {
          CONFIRMATION_QUESTION: (challengeName: string, teamName: string | undefined) =>
            teamName
              ? `Do you want to join the "${challengeName}" challenge as a member of the "${teamName}" team?`
              : `Do you want to join the "${challengeName}" challenge?`,
          PUBLIC_INVITE_QUESTION: (challengeName: string) =>
            `You've been invited to join the "${challengeName}" challenge. Would you like to:`,
          PUBLIC_INVITE_QUESTION_GROUP: (levelName = 'group') =>
            `Now that you've selected a ${levelName.toLowerCase()}, would you like to:`,
          PUBLIC_JOIN_TEAM: 'Assign me to a team',
          PUBLIC_CREATE_TEAM: 'Create a new team',
          CONFIRMATION_ACCEPT: 'You have successfully been added to the challenge!',
          CONFIRMATION_ACCEPT_TEAM_NAME: (teamName: string) =>
            `You have joined the "${teamName}" team!`,
          CONFIRMATION_ACCEPT_CAPTAIN_TEAM_NAME: (teamName: string) =>
            `"${teamName}" team was created and you are the team captain!`,
          CONFIRMATION_ACCEPT_CAPTAIN:
            'As the team captain, you can invite others to join your team.',
          CONFIRMATION_REJECT: 'You have declined the challenge invitation',
          NOT_FOUND:
            "You have been invited to join a challenge, but we couldn't find the challenge details.",
          GO_TO_CHALLENGE: 'Go to Challenge',
          ALREADY_PARTICIPANT: 'You are already a participant of this challenge.',
          TEAM_FULL: 'This team is already full.',
          SELECT_PARENT_GROUP: (challengeName: string, levelName = 'group') =>
            `You have been invited to join the "${challengeName}" challenge. Please select a ${levelName.toLowerCase()} to join:`,
        },
      },
    },
    NOTIFICATIONS: {
      INVITE: {
        CHALLENGE_PARTICIPANT_TITLE: 'Ready, Set, Fly! 🚀',
        CHALLENGE_PARTICIPANT_BODY: 'You have been invited to join a mileage challenge',
      },
      SETTINGS: {
        TITLE: 'Notification Settings',
        DESCRIPTION:
          'Our notifications are designed to be personalized and disabled as you see fit.',
        MUST_ENABLE_NATIVE: `You must give ${APP_NAME} permission to send you push notifications in your ${isIos ? 'iOS' : 'Android'} app settings before you can configure your notification settings.`,
        OPEN_NATIVE_SETTINGS: `Open ${APP_NAME} ${isIos ? 'iOS' : 'Android'} App Settings`,
        ENABLE_LABEL: 'Enable Notifications',
        PUSH_NOTIFICATION_SECTION: 'Push Notifications',
        WELLNESS_EDUCATION_SECTION: 'Wellness Education',
        SAVED_AUTOMATICALLY: 'All values are saved automatically.',
        RESET: 'Reset to defaults',
        CLOSE_MODAL: 'Close',
        WORKOUTS: {
          LABEL: '🏋️ Workout',
          HEADER: '🏋️ Workout Notification Settings',
          SCHEDULED_LABEL: '⌛️ Workout Scheduled',
          SCHEDULED_FREQUENCY_LABEL: '⏲ when a workout is scheduled',
          COMPLETE_LABEL: '✅ Workout Complete',
          COMPLETE_FREQUENCY_LABEL: '⏲ when you complete a workout',
        },
        CHALLENGES: {
          LABEL: '🏆 Challenge',
          HEADER: '🏆 Challenge Notification Settings',
          SETTING_LABEL: (type: PushNotificationChallengeTypes) =>
            patternMatch(type)
              .with(PushNotificationTypes.CHALLENGE_INVITE, () => ({
                label: '📩 Challenge Invite',
                frequencyLabel: '⏲ when invited',
              }))
              .with(PushNotificationTypes.CHALLENGE_USER_JOINED_FOR_TRAINER, () => ({
                label: '🅿 Participant joined from invite',
                frequencyLabel: '⏲ when a user joins a challenge from an invite',
              }))
              .with(PushNotificationTypes.CHALLENGE_USER_JOINED_FOR_CAPTAIN, () => ({
                label: '🙋 Teammate joined from invite',
                frequencyLabel: '⏲ when a teammate joins your team',
              }))
              .with(PushNotificationTypes.CHALLENGE_ENDED, () => ({
                label: '🏁 Challenge Ended',
                frequencyLabel: '⏲ when a challenge ends',
              }))
              .with(PushNotificationTypes.CHALLENGE_GROUP_MILESTONES, () => ({
                label: '📍 Challenge Milestones Hit',
                frequencyLabel: '⏲ ~4-6 times a challenge',
              }))
              .with(PushNotificationTypes.CHALLENGE_TEAM_DAILY_GOAL_MET, () => ({
                label: '🎯 Challenge Team Daily Goal Met',
                frequencyLabel: '⏲ ~2-4 times a challenge',
              }))
              .with(PushNotificationTypes.CHALLENGE_CHECK_IN, () => ({
                label: '⏰ Challenge Check Ins',
                frequencyLabel: '⏲ once a day, only after being inactive',
              }))
              .with(PushNotificationTypes.CHALLENGE_TEAM_POST_CREATED, () => ({
                label: '💬 Challenge Team Post',
                frequencyLabel: '⏲ when a team member posts',
              }))
              .with(PushNotificationTypes.CHALLENGE_ALL_TEAMS_POST_CREATED, () => ({
                label: '💬 Challenge All Teams Post',
                frequencyLabel: '⏲ when anyone posts in the challenge feed',
              }))
              .exhaustive(),
        },
        WELLNESS_EDUCATION: {
          BLOG: {
            LABEL: '🍎 Daily Wellness Blog',
            HEADER: '🍎 Daily Wellness Blog Notification Settings',
            WELLNESS_BLOG_LABEL: '🍎 Daily Wellness Blog',
            CATEGORY_HEADER: '⚙️ Blog Category Settings',
            CATEGORY_LABEL: (category: WellnessBlogCategories) =>
              patternMatch(category)
                .with(WellnessBlogCategories.EXERCISE, () => '🏃 Exercise')
                .with(WellnessBlogCategories.NUTRITION, () => '🥑 Nutrition')
                .with(WellnessBlogCategories.SLEEP, () => '😴 Sleep')
                .with(WellnessBlogCategories.STRESS_MANAGEMENT, () => '😌 Stress Management')
                .with(WellnessBlogCategories.HYDRATION, () => '💧 Hydration')
                .with(WellnessBlogCategories.CIRCADIAN_RHYTHM, () => '⏰ Circadian Rhythm')
                .with(WellnessBlogCategories.HUMAN_BIOLOGY, () => '🧬 Human Biology')
                .with(WellnessBlogCategories.SUNLIGHT, () => '☀️ Sunlight')
                .exhaustive(),
            TOGGLE_ALL_LABEL: 'Toggle All',
            TIME_LABEL: 'Time of day to receive notifications',
            FREQUENCY_LABEL: 'Notification Frequency (in days)',
          },
          QUIZ: {
            LABEL: '❓ Daily Wellness Quiz',
            HEADER: '❓ Daily Wellness Quiz Notification Settings',
            TIME_LABEL: 'Time of day to receive daily wellness quiz',
            WELLNESS_QUIZ_QUESTION_LABEL: '💬 Quiz Question',
            WELLNESS_QUIZ_QUESTION_FREQUENCY_LABEL: '⏲ at the configured time below',
          },
        },
        STREAKS: {
          LABEL: '🔥 Streak',
          HEADER: '🔥 Streak Notification Settings',
          MOVEMENT_STREAK_START_LABEL: '🔥🏃 Movement streak continue/start',
          MOVEMENT_STREAK_STAT_FREQUENCY_LABEL: '⏲ once a day, if recently been on app',
          MOVEMENT_STREAK_REMINDER_LABEL: '🔥⏳ Movement streak reminder',
          MOVEMENT_STREAK_REMINDER_FREQUENCY_LABEL: '⏲ once a day, while in a streak',
          QUIZ_STREAK_START_LABEL: '🔥❓ Quiz streak start/continue',
          QUIZ_STREAK_START_FREQUENCY_LABEL: '⏲ once a day, if recently been on app',
          QUIZ_STREAK_REMINDER_LABEL: '🔥⏳ Quiz streak reminder',
          QUIZ_STREAK_REMINDER_FREQUENCY_LABEL: '⏲ once a day, while on streak',
        },
      },
    },
    WELLNESS_BLOG_POST: {
      TITLE: 'Daily Wellness Blog Post',
    },
    WELLNESS_QUIZ: {
      TITLE: 'Daily Wellness Quiz',
      CLOSE: 'Close',
      START_QUIZ_BUTTON: 'Start Quiz',
      START_QUIZ_NO_TITLE_BUTTON: 'Daily Quiz',
      CORRECT: 'Correct! 🎉',
      INCORRECT_HINT: 'Not quite... try again!',
      DEFAULT_EXPLANATION: (answer?: string) =>
        answer ? `The correct answer is "${answer}"` : 'That is the correct answer.',
      ALREADY_COMPLETED_BUTTON: 'View Quiz',
      COMPLETED_LABEL: '✅ Completed',
      TOTAL_COMPLETED_LABEL: '✅ Complete',
      STREAK_LABEL: '🔥 Streak',
      STREAK: {
        STREAK_COMPLETE_TODAY: (streakNumber: number) =>
          QUIZ_STREAK_CONTINUE[(streakNumber - 1) % QUIZ_STREAK_CONTINUE.length],
        STREAK_CONTINUE_TODAY: "Don't lose your quiz streak!",
        START_STREAK_0_DAY: 'Answer a quiz question to start a streak',
      },
    },
    COACH: {
      HOME: {
        HEADER_TITLE: 'Coach Home',
        SUMMARY_HEADER: 'Summary',
        CLIENTS_HEADER: 'Clients',
        CREATE_USER_LABEL: 'Create User',
      },
      SUMMARY: {
        HEADER: 'All Time (excludes self)',
        NUM_OF_CLIENTS: 'Clients',
        NUM_OF_WORKOUTS: 'Client Workouts',
        DURATION: 'Workout Time (hrs)',
      },
    },
    ANDROID: {
      DATA_SOURCE_POP_UP: {
        HEADER: 'Select a data source',
        BODY: `${APP_NAME} sources data from Google Health Connect on Android. Since Health Connect supports multiple data sources, you must select which ones to use as the source of truth for your health data. You can always change this later in your Health Sync settings.`,
        CONFIRM_BUTTON: 'Confirm',
        TAKE_AWHILE: 'Reloading your data, this may take a few seconds...',
      },
    },
    WEIGHT: {
      HEADER: 'Weight',
      ADD_LABEL: 'Add',
      SAVE_LABEL: 'Save',
      CANCEL_LABEL: 'Cancel',
      DATE_LABEL: 'Date',
      TIME_LABEL: 'Time',
      WEIGHT_VALUE_LABEL: 'Weight (lbs)',
      ADD_WEIGHT_LABEL: 'Add Weight',
      EDIT_WEIGHT_LABEL: 'Edit Weight',
      ERROR_WEIGHT_VALUE: 'Invalid weight value',
      NO_WEIGHT_DATA: 'No weight recorded yet',
      NO_WEIGHT_DATA_HOME_SCREEN: 'No weight recorded',
      LAST_WEIGHT_LABEL: (formattedDate: string) => ` lbs, ${formattedDate}`,
    },
    SUMMARY_REPORTS: {
      HEADER: 'Summary Reports',
      CREATE_HEADER: 'Create Summary Report',
      USERS_LABEL: 'Users',
      SELECTED_USERS_LABEL: 'Selected Users',
      TYPE_LABEL: 'Report Type',
      NAME_LABEL: 'Report Name (optional)',
      ORGANIZATION_LABEL: 'Organization',
      CHALLENGES_LABEL: 'Challenge',
      START_DATE_LABEL: 'Start Date',
      END_DATE_LABEL: 'End Date',
      ADD_USER_LABEL: 'Add User',
      REMOVE_USER_LABEL: 'Remove User',
      CREATE_BUTTON_LABEL: 'Generate Summary Report',
      DURATION_LABEL: 'Duration: 7 days',
      EXPLANATION:
        'Generate summary PDF reports for the selected users. These reports will be generated and sent to the user over email when you click generate',
      WARNING_DESCRIPTION:
        "Note: The reports will use the user's current data when generating the report. Once the reports starts generating, it will be immediately sent over email, so be sure you're ready to send it.",
      NO_SUMMARY_REPORTS: 'No summary reports have been created yet',
      ALERT_DELETE: {
        HEADER: 'Delete Summary Report',
        BODY: 'Are you sure you want to delete this summary report? Any reports already sent will still exist, but you can still delete this to hide it in the summary report list.',
      },
      ACTION_SHEET: {
        DELETE: 'Delete Report',
        RECREATE_REPORT: 'Recreate Report & Email',
        OPEN: 'Open Report',
      },
    },
    FEED: {
      PAGE_HEADER: 'Organization Feed',
      SELECT_ORGANIZATION: 'Select your organization',
      CONFIRM_BUTTON: 'Go to organization feed',
      POSTS: {
        ACTION_SHEET: {
          EDIT: 'Edit Post',
          DELETE: 'Delete Post',
          CANCEL: 'Cancel',
        },
        ALERT_DELETE: {
          HEADER: 'Delete Post',
          BODY: 'Are you sure you want to delete this post? This action cannot be undone.',
        },
      },
    },
  }) as const;
/* eslint-enable sonarjs/no-nested-template-literals */
/* eslint-enable no-console  */
/* eslint-enable max-lines  */
